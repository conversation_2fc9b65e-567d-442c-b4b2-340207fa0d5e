import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import '../screens/post_detail_screen.dart';
import 'video_player_widget.dart';

class FeedItem extends StatefulWidget {
  final PostModel post;
  final bool isVisible;

  const FeedItem({
    super.key,
    required this.post,
    this.isVisible = true,
  });

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Background media content
          _buildMediaContent(),

          // Gradient overlay for better text readability (only for videos)
          if (widget.post.isVideo)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withValues(alpha: 77), // 0.3 opacity - even more reduced for videos
                  ],
                  stops: const [0.0, 0.8, 1.0], // Start even later to preserve more video area
                ),
              ),
            )
          else
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withValues(alpha: 102), // 0.4 opacity for images
                  ],
                  stops: const [0.0, 0.7, 1.0],
                ),
              ),
            ),

          // Right side action buttons
          Positioned(
            right: 16,
            bottom: 100,
            child: Column(
              children: [
                _buildActionButton(
                  icon:
                      widget.post.isLikedByCurrentUser
                          ? Icons.favorite
                          : Icons.favorite_border,
                  label: widget.post.likeCount.toString(),
                  color: widget.post.isLikedByCurrentUser ? Colors.red : Colors.white,
                  onPressed: () => _handleLike(context),
                ),
                const SizedBox(height: 24),
                _buildActionButton(
                  icon: Icons.chat_bubble_outline,
                  label: widget.post.commentCount.toString(),
                  color: Colors.white,
                  onPressed: () => _handleComment(context),
                ),
                const SizedBox(height: 24),
                _buildActionButton(
                  icon: Icons.share,
                  label: 'Share',
                  color: Colors.white,
                  onPressed: () => _handleShare(context),
                ),
                const SizedBox(height: 24),
                _buildActionButton(
                  icon: Icons.more_vert,
                  label: '',
                  color: Colors.white,
                  onPressed: () => _showPostOptions(context),
                ),
              ],
            ),
          ),

          // Bottom content info
          Positioned(
            left: 16,
            right: 80,
            bottom: 100,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User info
                Row(
                  children: [
                    _buildAvatar(),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.post.authorDisplayName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '@${widget.post.authorUsername}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Post content
                if (widget.post.content.isNotEmpty)
                  Text(
                    widget.post.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaContent() {
    if (widget.post.hasMedia && widget.post.mediaUrl != null) {
      if (widget.post.isImage) {
        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Image.network(
            widget.post.mediaUrl!,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              if (kDebugMode) {
                print('Error loading image: $error');
              }
              return _buildMediaPlaceholder('Image');
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.gfGreen,
                  ),
                ),
              );
            },
          ),
        );
      } else if (widget.post.isVideo) {
        // Use the video player widget
        return VideoPlayerWidget(
          videoUrl: widget.post.mediaUrl!,
          autoPlay: true,
          isVisible: widget.isVisible,
        );
      }
    }



    // Fallback to a gradient background
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    if (widget.post.avatarUrl != null && widget.post.avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
        radius: 20,
        backgroundImage: NetworkImage(widget.post.avatarUrl!),
        onBackgroundImageError: (_, __) {},
        child:
            widget.post.avatarUrl!.isEmpty
                ? Text(
                  widget.post.authorDisplayName.isNotEmpty
                      ? widget.post.authorDisplayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: AppColors.gfDarkBlue,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
      radius: 20,
      child: Text(
        widget.post.authorDisplayName.isNotEmpty
            ? widget.post.authorDisplayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 128), // 0.5 opacity
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    await postsProvider.toggleLike(widget.post.id);
  }

  void _handleComment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: widget.post)),
    );
  }

  void _handleShare(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }
}
