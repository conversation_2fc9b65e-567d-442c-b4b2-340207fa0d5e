#!/bin/sh

# Storage initialization script for GameFlex
# This script uploads initial media files to the Supabase storage bucket
# Files are organized by user and channel: /user/{user-id}/{channel-id}/{random-uuid}.{extension}

set -e

echo "🚀 Starting storage initialization..."

# Wait for storage service to be fully ready
echo "⏳ Waiting for storage service to be ready..."
sleep 15

# Define the mapping of files to users and channels (matching seed data)
# Format: "filename:user_id:channel_id:new_filename"
FILE_MAPPINGS="
cod_screenshot.jpg:00000000-0000-0000-0000-000000000003:10000000-0000-0000-0000-000000000001:a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg
diablo_screenshot.jpg:00000000-0000-0000-0000-000000000004:10000000-0000-0000-0000-000000000003:b2c3d4e5-f6g7-8901-bcde-f23456789012.jpg
minecraft_screenshot.webp:00000000-0000-0000-0000-000000000005:10000000-0000-0000-0000-000000000004:c3d4e5f6-g7h8-9012-cdef-************.webp
wow_screenshot.png:00000000-0000-0000-0000-000000000001:10000000-0000-0000-0000-000000000002:d4e5f6g7-h8i9-0123-def0-************.png
AI_Game.mp4:00000000-0000-0000-0000-000000000002:10000000-0000-0000-0000-000000000001:e5f6g7h8-i9j0-1234-efgh-************.mp4
"

# Function to check if storage is ready
check_storage_ready() {
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo "🔍 Checking storage readiness (attempt $attempt/$max_attempts)..."

        if curl -s -f "$SUPABASE_URL/storage/v1/bucket" \
           -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" > /dev/null 2>&1; then
            echo "✅ Storage service is ready!"
            return 0
        fi

        echo "⏳ Storage not ready yet, waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done

    echo "❌ Storage service failed to become ready after $max_attempts attempts"
    return 1
}

# Function to create storage bucket if it doesn't exist
create_bucket() {
    local bucket="media"

    echo "🪣 Checking if bucket '$bucket' exists..."

    # Check if bucket exists
    response=$(curl -s -w "\n%{http_code}" \
        -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
        "$SUPABASE_URL/storage/v1/bucket/$bucket")

    http_code=$(echo "$response" | tail -n1)

    if [ "$http_code" = "200" ]; then
        echo "✅ Bucket '$bucket' already exists"
        return 0
    fi

    echo "📦 Creating bucket '$bucket'..."

    # Create bucket
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
        -H "Content-Type: application/json" \
        -d '{
            "id": "media",
            "name": "media",
            "public": true,
            "file_size_limit": 52428800,
            "allowed_mime_types": ["image/jpeg", "image/png", "image/webp", "image/gif", "video/mp4", "video/webm"]
        }' \
        "$SUPABASE_URL/storage/v1/bucket")

    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo "✅ Successfully created bucket '$bucket'"
        return 0
    else
        echo "❌ Failed to create bucket '$bucket' (HTTP $http_code)"
        echo "Response: $response_body"
        return 1
    fi
}

# Function to get MIME type based on file extension
get_mime_type() {
    local file_name="$1"
    local extension="${file_name##*.}"

    # Convert to lowercase using tr
    extension=$(echo "$extension" | tr '[:upper:]' '[:lower:]')

    case "$extension" in
        jpg|jpeg) echo "image/jpeg" ;;
        png) echo "image/png" ;;
        webp) echo "image/webp" ;;
        gif) echo "image/gif" ;;
        mp4) echo "video/mp4" ;;
        webm) echo "video/webm" ;;
        *) echo "application/octet-stream" ;;
    esac
}

# Function to upload a file to storage with user/channel structure
upload_file_structured() {
    local file_path="$1"
    local user_id="$2"
    local channel_id="$3"
    local new_filename="$4"
    local bucket="media"

    # Create the structured path: user/{user-id}/{channel-id}/{filename}
    local storage_path="user/$user_id/$channel_id/$new_filename"

    echo "📤 Uploading $new_filename to $storage_path..."

    # Get MIME type based on file extension
    mime_type=$(get_mime_type "$new_filename")

    # Upload file using Supabase Storage API
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Authorization: Bearer $SUPABASE_SERVICE_KEY" \
        -H "Content-Type: $mime_type" \
        -H "x-upsert: true" \
        --data-binary "@$file_path" \
        "$SUPABASE_URL/storage/v1/object/$bucket/$storage_path")

    # Extract HTTP code from last line
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)

    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo "✅ Successfully uploaded $storage_path (MIME: $mime_type)"
    else
        echo "❌ Failed to upload $storage_path (HTTP $http_code)"
        echo "Response: $response_body"
        # Don't exit on failure, continue with other files
    fi
}

# Check if storage service is ready
if ! check_storage_ready; then
    echo "❌ Cannot proceed without storage service"
    exit 1
fi

# Create storage bucket if it doesn't exist
if ! create_bucket; then
    echo "❌ Cannot proceed without storage bucket"
    exit 1
fi

# Check if media directory exists
if [ ! -d "/media" ]; then
    echo "❌ Media directory not found at /media"
    exit 1
fi

# Upload all media files using the structured approach
echo "📁 Found media files:"
ls -la /media/

file_count=0
echo "$FILE_MAPPINGS" | while IFS= read -r line; do
    # Skip empty lines
    [ -z "$line" ] && continue

    # Parse the mapping: filename:user_id:channel_id:new_filename
    original_filename=$(echo "$line" | cut -d: -f1)
    user_id=$(echo "$line" | cut -d: -f2)
    channel_id=$(echo "$line" | cut -d: -f3)
    new_filename=$(echo "$line" | cut -d: -f4)

    file_path="/media/$original_filename"

    if [ -f "$file_path" ]; then
        echo "🔄 Processing $original_filename → user/$user_id/$channel_id/$new_filename"
        upload_file_structured "$file_path" "$user_id" "$channel_id" "$new_filename"
        file_count=$((file_count + 1))
    else
        echo "⚠️  File not found: $original_filename"
    fi
done

echo "✅ Storage initialization completed! Processed $file_count files."

# Keep container running for a moment to see logs
sleep 5
